<?php
include('confing/common.php'); 
if ($islogin != 1) {
    exit(json_encode(['code' => -1, 'msg' => '登录过期请重新登录', 'redirect' => '/index/login']));
}
$php_Self = substr($_SERVER['PHP_SELF'],strripos($_SERVER['PHP_SELF'],"/")+1);
    if($php_Self!="apiadmin.php"){
        $msg = '%E6%96%87%E4%BB%B6%E9%94%99%E8%AF%AF';
        $msg = urldecode($msg);
       exit(json_encode(['code' => -1, 'msg' => $msg]));}
    if($userrow['uid'] != 1){
        $msg = '%E6%96%87%E4%BB%B6%E9%94%99%E8%AF%AF';
        $msg = urldecode($msg);
       exit(json_encode(['code' => -1, 'msg' => $msg]));}
$act = isset($_GET["act"]) ? daddslashes($_GET["act"]) : null;
@header("Content-Type: application/json; charset=UTF-8");
    switch($act){
        //货源分类一键对接商品管理质押密价等级
        //系统设置
        	case 'webset':	    
		    parse_str(daddslashes($_POST['data']),$row);
		    if ($userrow['uid']!=1) {
		        exit('{"code":-1,"msg":"滚，傻逼！你没妈了？"}');
		   }else if($userrow['uid']==1) {
		    //var_dump($row);
		   	foreach($row as $k => $value){
		   	 if($k=='dklcookie' || $k=='nanatoken' || $k=='akcookie' || $k=='vpercookie'){
		   	 	$value=authcode($value,'ENCODE','qingka');
		   	 }	
			 $DB->query("UPDATE `qingka_wangke_config` SET k='{$value}' WHERE v='{$k}'");
		    }
		   exit('{"code":1,"msg":"修改成功"}');
		   }
	break;
        //等级模块
    	case 'djlist':
	    $page=trim(strip_tags(daddslashes($_POST['page'])));
		$pagesize=500;
	    $pageu = ($page - 1) * $pagesize;//当前界面		
		if($userrow['uid']!='1'){
          	jsonReturn(-1,"滚");
		}
		$a=$DB->query("select * from qingka_wangke_dengji");
		$count1=$DB->count("select count(*) from qingka_wangke_dengji");
		while($row=$DB->fetch($a)){
	   	   $data[]=array(
	   	        'id'=>$row['id'],
	   	        'sort'=>$row['sort'],
   	            'name'=>$row['name'],
	   	        'rate'=>$row['rate'],
	   	        'money'=>$row['money'],
	   	        'addkf'=>$row['addkf'],
	   	        'gjkf'=>$row['gjkf'],
	   	        'status'=>$row['status'],
	   	        'time'=>$row['time'],
	   	   );
	    }
	    foreach ($data as $key => $row)
            {
                $id[$key] = $row['id'];
                $sort[$key]  = $row['sort'];
                $name[$key] = $row['name'];
                $rate[$key] = $row['rate'];
                $money[$key] = $row['money'];
                $addkf[$key] = $row['addkf'];
                $gjkf[$key] = $row['gjkf'];
                $status[$key] = $row['status'];
                $time[$key] = $row['time'];
            }
	    array_multisort($sort, SORT_ASC, $rate, SORT_ASC, $data);
	    $last_page=ceil($count1/$pagesize);//取最大页数
	    $data=array('code'=>1,'data'=>$data,"current_page"=>(int)$page,"last_page"=>$last_page);
	    exit(json_encode($data));
	break;
	case 'dj':
	    $data = $_POST['data'];
	    $active = trim($_POST['active']);
	    $id = trim($data['id']);
	    $sort = trim($data['sort']);
	    $name = trim($data['name']);
	    $rate = trim($data['rate']);
	    $money = trim($data['money']);
	    $status = trim($data['status']);
	    $addkf = trim($data['addkf']);
	    $gjkf = trim($data['gjkf']);

		if($userrow['uid']!='1'){jsonReturn(-1,"滚！");}
        if($active=='1'){
        	$sql = "insert into qingka_wangke_dengji (sort,name,rate,money,addkf,gjkf,status,time) values (?,?,?,?,?,?,?,NOW())";
        	$params = [$sort, $name, $rate, $money, $addkf, $gjkf, 1];
			$result = $DB->prepare_query($sql, $params);
        	if($result){
        		jsonReturn(1,"添加成功");
        	}else{
        		jsonReturn(-1,"添加失败");
        	}
        }elseif($active=='2'){
        	$sql = "update qingka_wangke_dengji set `sort`=?,`name`=?,`rate`=?,`money`=?,`addkf`=?,`gjkf`=?,`status`=? where id=?";
        	$params = [$sort, $name, $rate, $money, $addkf, $gjkf, $status, $id];
			$result = $DB->prepare_query($sql, $params);
        	if($result){
        		jsonReturn(1,"修改成功");
        	}else{
        		jsonReturn(-1,"修改失败");
        	}
        }else{
        	jsonReturn(-1,"不知道你在干什么");
        }
	break;
		case 'dj_del':
	    $id = $_POST['id'];
	    $authcode = $_POST['authcode'];
	    if($userrow['uid'] != '1') {
	        jsonReturn(-1, "无权限");
	    }
	    if(empty($authcode)) {
	        jsonReturn(-1, "验证码不能为空");
	    }
	    if($authcode != $verification) {
	        jsonReturn(-1, "验证码错误");
	    }
		$sql = "DELETE FROM qingka_wangke_dengji WHERE id=?";
		$params = [$id];
		$stmt = $DB->prepare_query($sql, $params);
	    if($stmt) {
	        jsonReturn(1, "删除成功");
	    } else {
	        jsonReturn(-1, "删除失败");
	    }
	break;
	//货源模块
    	case 'huoyuanlist':
		if($userrow['uid']=='1'){
			$a=$DB->query("select * from qingka_wangke_huoyuan");
		    while($row=$DB->fetch($a)){
		   	   $data[]=$row;
		    }
		    $data=array('code'=>1,'data'=>$data);
		    exit(json_encode($data));
	  }else{
	    	exit('{"code":-2,"msg":"你在干啥"}');
	  }
	break;	
    case 'uphuoyuan':
	     if($userrow['uid']==1){
	      parse_str(daddslashes($_POST['data']),$row);
          if($row['action']=='add'){
          	$DB->query("insert into qingka_wangke_huoyuan (pt,name,url,user,pass,token,ip,cookie,addtime) values ('{$row['pt']}','{$row['name']}','{$row['url']}','{$row['user']}','{$row['pass']}','{$row['token']}','{$row['ip']}','{$row['cookie']}',NOW())");
    	    exit('{"code":1,"msg":"操作成功"}');
          }else{		   
	        $DB->query("update `qingka_wangke_huoyuan` set `pt`='{$row['pt']}',`name`='{$row['name']}',`url`='{$row['url']}',`user`='{$row['user']}',`pass`='{$row['pass']}',`token`='{$row['token']}',`ip`='{$row['ip']}',`cookie`='{$row['cookie']}',`endtime`=NOW() where hid='{$row['hid']}' ");	        
	        exit('{"code":1,"msg":"操作成功"}');
	      }
	    }else{
		    exit('{"code":-2,"msg":"无权限"}');
		}
	break;

	case 'huoyuandel':
	    parse_str(daddslashes($_POST['data']),$row);
	    $hid = $row['hid'];
	    $authcode = $row['authcode'];
	    if($userrow['uid'] != '1') {
	        jsonReturn(-1, "无权限");
	    }
	    if(empty($authcode)) {
	        jsonReturn(-1, "验证码不能为空");
	    }
	    if($authcode != $verification) {
	        jsonReturn(-1, "验证码错误");
	    }
		$sql = "DELETE FROM qingka_wangke_huoyuan WHERE hid=?";
		$params = [$hid];
		$stmt = $DB->prepare_query($sql, $params);
	    if($stmt) {
	        jsonReturn(1, "删除成功");
	    } else {
	        jsonReturn(-1, "删除失败");
	    }
	break;
	//分类模块
case 'fllist':
    $page = (int)$_POST['page'];
    $pagesize = 500;
    $pageu = ($page - 1) * $pagesize;
    if($userrow['uid']!='1'){
        jsonReturn(-1,"滚");
    }
    $sql = "SELECT * FROM qingka_wangke_fenlei";
    $data = [];
    $stmt = $DB->prepare_query($sql);
    if ($stmt) {
        $result = $stmt->get_result();
        while($row = $result->fetch_assoc()){
            $data[] = [
                'id' => $row['id'],
                'sort' => $row['sort'],
                'name' => $row['name'],
                'rate' => $row['rate'],
                'money' => $row['money'],
                'addkf' => $row['addkf'],
                'gjkf' => $row['gjkf'],
                'status' => $row['status'],
                'time' => $row['time'],
                'text' => $row['text'],
            ];
        }
        $stmt->close();
    }
    $count_sql = "SELECT COUNT(*) FROM qingka_wangke_fenlei";
    $count1 = $DB->prepare_count($count_sql);
    foreach ($data as $key => $row){
        $id[$key] = $row['id'];
        $sort[$key] = $row['sort'];
        $name[$key] = $row['name'];
        $rate[$key] = $row['rate'];
        $money[$key] = $row['money'];
        $addkf[$key] = $row['addkf'];
        $gjkf[$key] = $row['gjkf'];
        $status[$key] = $row['status'];
        $time[$key] = $row['time'];
        $text[$key] = $row['text'];
    }
    array_multisort($sort, SORT_ASC, $rate, SORT_ASC, $data);
    $last_page = ceil($count1/$pagesize);
    $data = ['code'=>1,'data'=>$data,"current_page"=>(int)$page,"last_page"=>$last_page];
    exit(json_encode($data));
break;

case 'fl':
    $data = $_POST['data'];
    $active = trim($_POST['active']);
    if ($userrow['uid'] != '1') {
        jsonReturn(-1, "滚！");
    }
    if ($active == '1') {
        $sql = "INSERT INTO qingka_wangke_fenlei (sort, name, text, status, time) VALUES (?, ?, ?, '1', NOW())";
        $success = true;
        foreach ($data as $category) {
            $sort = trim($category['sort']);
            $name = trim($category['name']);
            $text = trim($category['text']);
            if (empty($name)) {
                continue; // Skip empty entries
            }
            $params = [$sort, $name, $text];
            $stmt = $DB->prepare_query($sql, $params);
            if (!$stmt) {
                $success = false;
            }
        }
        if ($success) {
            jsonReturn(1, "添加成功");
        } else {
            jsonReturn(-1, "部分或全部添加失败");
        }
    } elseif ($active == '2') {
        $id = trim($data['id']);
        $sort = trim($data['sort']);
        $name = trim($data['name']);
        $text = trim($data['text']);
        $status = trim($data['status']);
        $sql = "UPDATE qingka_wangke_fenlei SET `sort`=?, `name`=?, `text`=?, `status`=? WHERE id=?";
        $params = [$sort, $name, $text, $status, $id];
        $stmt = $DB->prepare_query($sql, $params);
        if($stmt){
            jsonReturn(1, "修改成功");
        } else {
            jsonReturn(-1, "修改失败");
        }
    } else {
        jsonReturn(-1, "不知道你在干什么");
    }
break;

case 'fl_del':
    $id = $_POST['id'];
    $authcode = $_POST['authcode'];
    if($userrow['uid'] != '1') {
        jsonReturn(-1, "无权限");
    }
    if(empty($authcode)) {
        jsonReturn(-1, "验证码不能为空");
    }
    if($authcode != $verification) {
        jsonReturn(-1, "验证码错误");
    }
    $sql = "DELETE FROM qingka_wangke_fenlei WHERE id=?";
    $params = [$id];
    $stmt = $DB->prepare_query($sql, $params);
    if($stmt) {
        jsonReturn(1, "删除成功");
    } else {
        jsonReturn(-1, "删除失败");
    }
break;
	//商品管理模块
	case 'classlist':
    $page = trim(strip_tags(daddslashes($_POST['page'])));
    $keyword = trim(strip_tags(daddslashes($_POST['keyword'])));
    $fenlei = trim(strip_tags(daddslashes($_POST['fenlei'])));
    $huoyuan = trim(strip_tags(daddslashes($_POST['huoyuan'])));
    $shangjiastatus = trim(strip_tags(daddslashes($_POST['shangjiastatus'])));
    $pagesize = 100;
    $pageu = ($page - 1) * $pagesize; //当前界面		
    $where = [];
    if (!empty($keyword)) {
        $where[] = "name LIKE '%$keyword%'";
    }
    if (!empty($fenlei)) {
        $where[] = "fenlei = '$fenlei'";
    }
    if (!empty($huoyuan)) {
        $where[] = "docking = '$huoyuan'";
    }
    if ($shangjiastatus !== '') {
        $where[] = "status = '$shangjiastatus'";
    }
    $where = !empty($where) ? "WHERE " . implode(" AND ", $where) : "";
    $count1 = $DB->count("SELECT COUNT(*) FROM qingka_wangke_class $where");
    $last_page = ceil($count1 / $pagesize); //取最大页数
    if ($userrow['uid'] == '1') {
        $a = $DB->query("SELECT * FROM qingka_wangke_class $where LIMIT $pageu, $pagesize");
        while ($row = $DB->fetch($a)) {
            $c = $DB->get_row("SELECT * FROM qingka_wangke_huoyuan WHERE hid='{$row['queryplat']}'");
            $d = $DB->get_row("SELECT * FROM qingka_wangke_huoyuan WHERE hid='{$row['docking']}'");
            $row['cx_name'] = $c['name'];
            $row['add_name'] = $d['name'];
            if ($row['queryplat'] == '0') {
                $row['cx_name'] = '自营';
            }
            if ($row['docking'] == '0') {
                $row['add_name'] = '自营';
            }
            $row['newPrice'] = $row['price'];
            $row['newSort'] = $row['sort'];
            $data[] = $row;
        }
        foreach ($data as $key => $rows) {
            $sort[$key] = $rows['sort'];
            $cid[$key] = $rows['cid'];
            $name[$key] = $rows['name'];
            $getnoun[$key] = $rows['getnoun'];
            $noun[$key] = $rows['noun'];
            $price[$key] = $rows['price'];
            $queryplat[$key] = $rows['queryplat'];
            $yunsuan[$key] = $rows['yunsuan'];
            $content[$key] = $rows['content'];
            $addtime[$key] = $rows['addtime'];
            $status[$key] = $rows['status'];
            $cx_names[$key] = $rows['cx_names'];
            $add_name[$key] = $rows['add_name'];
        }
        array_multisort($sort, SORT_ASC, $cid, SORT_DESC, $data);
        $data = array('code' => 1, 'data' => $data, "current_page" => (int)$page, "last_page" => $last_page);
        exit(json_encode($data));
    } else {
        exit('{"code":-2,"msg":"你在干啥"}');
    }
    break;


case 'deleteclass':
    $cid = trim($_POST['cid']);
    if ($userrow['uid'] == 1) {
        $sql = "DELETE FROM qingka_wangke_class WHERE cid = ?";
        $result = $DB->prepare_query($sql, [$cid]);
        if($result){
            exit('{"code":1,"msg":"操作成功"}');
        }else{
            exit('{"code":-1,"msg":"操作失败"}');
        }
    } else {
        exit('{"code":-2,"msg":"无权限"}');
    }
    break;

case 'batchdeleteclass':
    if ($userrow['uid'] == 1) {
        if (isset($_POST['cids']) && is_array($_POST['cids'])) {
            $cids = array_map('intval', $_POST['cids']);
            foreach ($cids as $cid) {
                $sql = "DELETE FROM qingka_wangke_class WHERE cid = ?";
                $DB->prepare_query($sql, [$cid]);
            }
            exit('{"code":1,"msg":"操作成功"}');
        } else {
            exit('{"code":-1,"msg":"无效的参数"}');
        }
    } else {
        exit('{"code":-2,"msg":"无权限"}');
    }
    break;

case 'batchupdatestatus':
    $status = $_POST['status'];
    if ($userrow['uid'] == 1) {
        if (isset($_POST['cids']) && is_array($_POST['cids'])) {
            $cids = array_map('intval', $_POST['cids']);
            foreach ($cids as $cid) {
                $sql = "UPDATE qingka_wangke_class SET status = ? WHERE cid = ?";
                $DB->prepare_query($sql, [$status, $cid]);
            }
            exit('{"code":1,"msg":"操作成功"}');
        } else {
            exit('{"code":-1,"msg":"无效的参数"}');
        }
    } else {
        exit('{"code":-2,"msg":"无权限"}');
    }
    break;
    
case 'batchupdatepricesort':
    if ($userrow['uid'] == 1) {
        if (isset($_POST['updates']) && is_array($_POST['updates'])) {
            $updates = $_POST['updates'];
            foreach ($updates as $update) {
                $cid = intval($update['cid']);
                $newPrice = floatval($update['newPrice']);
                $newSort = intval($update['newSort']);
                $newQueryPlat = $update['newQueryPlat'] ?? '';
                $newDocking = $update['newDocking'] ?? '';

                $sql = "UPDATE qingka_wangke_class SET price = ?, sort = ?, getnoun = ?, noun = ? WHERE cid = ?";
                $params = [$newPrice, $newSort, $newQueryPlat, $newDocking, $cid];
                $DB->prepare_query($sql, $params);
            }
            echo '{"code":1,"msg":"操作成功"}';
        } else {
            echo '{"code":-1,"msg":"无效的参数"}';
        }
    } else {
        echo '{"code":-2,"msg":"无权限"}';
    }
    break;

case 'upclass':
    parse_str($_POST['data'], $row);
    if ($userrow['uid'] == 1) {
        if ($row['action'] == 'add') {
            $sql = "INSERT INTO qingka_wangke_class (sort, name, getnoun, noun, price, queryplat, docking, content, addtime, status, fenlei) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
            $params = [$row['sort'], $row['name'], $row['getnoun'], $row['noun'], $row['price'], $row['queryplat'], $row['docking'], $row['content'], $date, $row['status'], $row['fenlei']];
            $DB->prepare_query($sql, $params);
            exit('{"code":1,"msg":"操作成功"}');
        } else {
            $sql = "UPDATE qingka_wangke_class SET sort = ?, name = ?, getnoun = ?, noun = ?, price = ?, queryplat = ?, docking = ?, yunsuan = ?, content = ?, status = ?, fenlei = ? WHERE cid = ?";
            $params = [$row['sort'], $row['name'], $row['getnoun'], $row['noun'], $row['price'], $row['queryplat'], $row['docking'], $row['yunsuan'], $row['content'], $row['status'], $row['fenlei'], $row['cid']];
            $DB->prepare_query($sql, $params);
            exit('{"code":1,"msg":"操作成功"}');
        }
    } else {
        exit('{"code":-2,"msg":"无权限"}');
    }
    break;
    //一键对接模块
    case 'updatekeywords':
        if ($userrow['uid'] == 1) {
            $oldKeyword = $_POST['oldKeyword'];
            $newKeyword = $_POST['newKeyword'];
            $effectScope = $_POST['effectScope'];
            $scopeId = $_POST['scopeId'];

            $where = "1";
            if ($effectScope == 'category') {
                $where = "`fenlei` = ?";
                $params = [$scopeId];
            } elseif ($effectScope == 'docking') {
                $where = "`docking` = ?";
                $params = [$scopeId];
            } else {
                $params = [];
            }
            $sql = "UPDATE `qingka_wangke_class` SET `name` = REPLACE(`name`, ?, ?) WHERE $where";
            $params = array_merge([$oldKeyword, $newKeyword], $params);

            $stmt = $DB->prepare_query($sql, $params);
            $result = $stmt !== false;
            $stmt->close();
            if ($result) {
                echo json_encode(['code' => 1, 'msg' => '关键词替换成功']);
            } else {
                echo json_encode(['code' => 0, 'msg' => '关键词替换失败']);
            }
        } else {
            exit('{"code":-1,"msg":"无权限"}');
        }
        break;

    case 'addprefix':
        if ($userrow['uid'] == 1) {
            $prefix = $_POST['prefix'];
            $prefixEffectScope = $_POST['prefixEffectScope'];
            $prefixScopeId = $_POST['prefixScopeId'];

            $where = "1";
            if ($prefixEffectScope == 'category') {
                $where = "`fenlei` = ?";
                $params = [$prefixScopeId];
            } elseif ($prefixEffectScope == 'docking') {
                $where = "`docking` = ?";
                $params = [$prefixScopeId];
            } else {
                $params = [];
            }
            $sql = "UPDATE `qingka_wangke_class` SET `name` = CONCAT(?, `name`) WHERE $where";
            array_unshift($params, $prefix);

            $stmt = $DB->prepare_query($sql, $params);
            $result = $stmt !== false;
            $stmt->close();
            if ($result) {
                echo json_encode(['code' => 1, 'msg' => '前缀添加成功']);
            } else {
                echo json_encode(['code' => 0, 'msg' => '前缀添加失败']);
            }
        } else {
            exit('{"code":-1,"msg":"无权限"}');
        }
        break;
        case 'checkbalance':
    if ($userrow['uid'] == 1) {
    $hid = intval($_POST['hid']);
    if (!$hid) {
        jsonReturn(0, '请选择货源');
    }
    $row = $DB->get_row("SELECT * FROM `qingka_wangke_huoyuan` WHERE hid = '$hid' LIMIT 1");
    if (!$row) {
        jsonReturn(0, '未找到该货源');
    }
    $url = $row['url'];
    $user = $row['user'];
    $pass = $row['pass'];
    $name = $row['name'];
    $er_url = $url . "/api.php?act=getmoney";
    $data = array("uid" => $user, "key" => $pass);
    $result = get_url($er_url, $data);
    $result_array = json_decode($result, true);
    if (isset($result_array['money'])) {
        $balance = $result_array['money'];
        $message = "当前接口 $name 余额为 $balance";
        //$DB->query("UPDATE qingka_wangke_huoyuan SET money='{$balance}' WHERE hid='{$hid}'");
        jsonReturn(1, $message);
    } else {
        jsonReturn(0, '查询余额失败');
    }
    }else{
        	exit('{"code":-1,"msg":"无权限"}');
       }
    break;

case 'checkdeployedcount':
    if ($userrow['uid'] == 1) {
    $hid = intval($_POST['hid']);
    if (!$hid) {
        jsonReturn(0, '请选择货源');
    }
    $count1 = $DB->count("select count(*) from qingka_wangke_class where docking = '{$hid}' ");
    $message = "当前货源已上架数为 $count1";
    jsonReturn(1, $message);
    }else{
        	exit('{"code":-1,"msg":"无权限"}');
       }
    break;
    case 'fetchAllProducts':
    if ($userrow['uid'] == 1) {
        $hid = intval($_POST['hid']);
        if (!$hid) {
            jsonReturn(0, '请选择货源');
        }
        $a = $DB->get_row("SELECT * FROM qingka_wangke_huoyuan WHERE hid='{$hid}'");
        if (!$a) {
            jsonReturn(-1, "货源信息不存在");
        }

        $data = array("uid" => $a["user"], "key" => $a["pass"]);
        $er_url = "{$a["url"]}/api.php?act=getclass";
        $result = get_url($er_url, $data);
        $result1 = json_decode($result, true);

        if (json_last_error() !== JSON_ERROR_NONE || !isset($result1["data"])) {
            jsonReturn(-1, "API 返回数据格式错误或缺失");
        }

        $products = $result1["data"];

        // 检查是否为29系统（通过平台类型或URL判断）
        $is_29_system = false;
        if (strpos($a["pt"], "29") !== false || strpos($a["name"], "29") !== false) {
            $is_29_system = true;
        }

        // 只有29系统才处理分类信息
        $upstream_categories = array();
        $category_debug_info = array();

        if ($is_29_system) {
            // 尝试多种可能的分类API接口（参考本系统的API结构）
            $fenlei_data = array("uid" => $a["user"], "key" => $a["pass"]);
            $fenlei_apis = array(
                'get_categories' => "{$a["url"]}/apisub.php?act=get_categories",  // 参考本系统的分类接口
                'getfenlei' => "{$a["url"]}/api.php?act=getfenlei",              // 传统的getfenlei接口
                'fllist' => "{$a["url"]}/apiadmin.php?act=fllist",               // 管理端分类列表接口
                'getclass_categories' => "{$a["url"]}/api.php?act=getclass&type=categories", // 可能的分类参数
                'category_list' => "{$a["url"]}/api.php?act=category_list"       // 另一种可能的接口名
            );

            foreach ($fenlei_apis as $api_name => $fenlei_url) {
                $fenlei_result = get_url($fenlei_url, $fenlei_data);
                $fenlei_result1 = json_decode($fenlei_result, true);

                // 记录调试信息
                $category_debug_info[$api_name] = array(
                    'url' => $fenlei_url,
                    'raw_response' => substr($fenlei_result, 0, 500), // 只记录前500字符
                    'parsed_response' => $fenlei_result1,
                    'success' => false
                );

                // 检查不同的返回格式
                if ($fenlei_result1) {
                    $categories_data = null;

                    // 检查多种可能的成功状态
                    $is_success = false;
                    if (isset($fenlei_result1["code"]) && $fenlei_result1["code"] == 1) {
                        $is_success = true;
                    } elseif (isset($fenlei_result1["status"]) && $fenlei_result1["status"] == "success") {
                        $is_success = true;
                    } elseif (isset($fenlei_result1["success"]) && $fenlei_result1["success"] == true) {
                        $is_success = true;
                    }

                    if ($is_success) {
                        // 格式1: 直接在data字段中
                        if (isset($fenlei_result1["data"]) && is_array($fenlei_result1["data"])) {
                            $categories_data = $fenlei_result1["data"];
                        }
                        // 格式2: 在data.data字段中（分页格式）
                        elseif (isset($fenlei_result1["data"]["data"]) && is_array($fenlei_result1["data"]["data"])) {
                            $categories_data = $fenlei_result1["data"]["data"];
                        }
                        // 格式3: 在categories字段中
                        elseif (isset($fenlei_result1["categories"]) && is_array($fenlei_result1["categories"])) {
                            $categories_data = $fenlei_result1["categories"];
                        }
                        // 格式4: 直接是数组
                        elseif (is_array($fenlei_result1) && !isset($fenlei_result1["code"])) {
                            $categories_data = $fenlei_result1;
                        }

                        if ($categories_data) {
                            $category_debug_info[$api_name]['success'] = true;
                            $category_debug_info[$api_name]['categories_count'] = count($categories_data);

                            foreach ($categories_data as $category) {
                                // 检查不同的字段名组合
                                $cat_id = null;
                                $cat_name = null;

                                if (isset($category['id'])) {
                                    $cat_id = $category['id'];
                                } elseif (isset($category['cid'])) {
                                    $cat_id = $category['cid'];
                                } elseif (isset($category['category_id'])) {
                                    $cat_id = $category['category_id'];
                                }

                                if (isset($category['name'])) {
                                    $cat_name = $category['name'];
                                } elseif (isset($category['title'])) {
                                    $cat_name = $category['title'];
                                } elseif (isset($category['category_name'])) {
                                    $cat_name = $category['category_name'];
                                }

                                if ($cat_id && $cat_name) {
                                    $upstream_categories[$cat_id] = $cat_name;
                                }
                            }

                            // 如果成功获取到分类，就不再尝试其他接口
                            if (!empty($upstream_categories)) {
                                break;
                            }
                        }
                    }
                }
            }
        }

        // 如果没有从API获取到分类信息，尝试从商品数据中提取
        if ($is_29_system && empty($upstream_categories)) {
            $extracted_categories = array();
            foreach ($products as $product) {
                if (isset($product['fenlei']) && isset($product['fenleiname']) && !empty($product['fenleiname'])) {
                    $extracted_categories[$product['fenlei']] = $product['fenleiname'];
                }
            }
            if (!empty($extracted_categories)) {
                $upstream_categories = $extracted_categories;
                $category_debug_info['extracted_from_products'] = array(
                    'success' => true,
                    'categories_count' => count($extracted_categories),
                    'method' => 'extracted from product fenleiname field'
                );
            }
        }

        // 为每个商品添加分类信息（ID和名称）
        foreach ($products as &$product) {
            if ($is_29_system && isset($product['fenlei'])) {
                $fenlei_id = $product['fenlei'];
                $fenlei_name = '';

                // 优先级：1.从分类API获取 2.商品数据中的fenleiname 3.默认显示
                if (isset($upstream_categories[$fenlei_id])) {
                    $fenlei_name = $upstream_categories[$fenlei_id];
                } else if (isset($product['fenleiname']) && !empty($product['fenleiname'])) {
                    $fenlei_name = $product['fenleiname'];
                    // 如果商品有分类名称但分类字典中没有，添加到字典中
                    if (!isset($upstream_categories[$fenlei_id])) {
                        $upstream_categories[$fenlei_id] = $fenlei_name;
                    }
                } else {
                    $fenlei_name = "未知分类";
                }

                // 同时保存分类ID和分类名称
                $product['fenlei_id'] = $fenlei_id;
                $product['fenlei_name'] = $fenlei_name;
                $product['fenlei_display'] = "ID:" . $fenlei_id . " - " . $fenlei_name;
            } else {
                // 非29系统不显示分类信息
                $product['fenlei_id'] = null;
                $product['fenlei_name'] = null;
                $product['fenlei_display'] = null;
            }
        }

        // 添加调试信息
        $debug_info = array(
            'is_29_system' => $is_29_system,
            'platform_type' => $a["pt"],
            'platform_name' => $a["name"],
            'platform_url' => $a["url"],
            'categories_count' => count($upstream_categories),
            'products_count' => count($products),
            'category_api_attempts' => $category_debug_info,
            'sample_product' => isset($products[0]) ? $products[0] : null
        );

        exit(json_encode([
            'code' => 1,
            'products' => $products,
            'categories' => $upstream_categories,
            'is_29_system' => $is_29_system,
            'debug' => $debug_info
        ]));
    } else {
        exit('{"code":-1,"msg":"无权限"}');
    }
    break;

case 'test29Categories':
    if ($userrow['uid'] == 1) {
        $hid = intval($_POST['hid']);
        if (!$hid) {
            jsonReturn(0, '请选择货源');
        }
        $a = $DB->get_row("SELECT * FROM qingka_wangke_huoyuan WHERE hid='{$hid}'");
        if (!$a) {
            jsonReturn(-1, "货源信息不存在");
        }

        // 检查是否为29系统
        $is_29_system = false;
        if (strpos($a["pt"], "29") !== false || strpos($a["name"], "29") !== false) {
            $is_29_system = true;
        }

        if (!$is_29_system) {
            jsonReturn(-1, "该货源不是29系统");
        }

        $test_results = array();
        $fenlei_data = array("uid" => $a["user"], "key" => $a["pass"]);

        // 测试多种分类API接口
        $fenlei_apis = array(
            'get_categories' => "{$a["url"]}/apisub.php?act=get_categories",
            'getfenlei' => "{$a["url"]}/api.php?act=getfenlei",
            'fllist' => "{$a["url"]}/apiadmin.php?act=fllist"
        );

        foreach ($fenlei_apis as $api_name => $fenlei_url) {
            $start_time = microtime(true);
            $fenlei_result = get_url($fenlei_url, $fenlei_data);
            $end_time = microtime(true);

            $test_results[$api_name] = array(
                'url' => $fenlei_url,
                'response_time' => round(($end_time - $start_time) * 1000, 2) . 'ms',
                'raw_response' => $fenlei_result,
                'parsed_response' => json_decode($fenlei_result, true),
                'categories_found' => 0
            );

            $fenlei_result1 = json_decode($fenlei_result, true);
            if ($fenlei_result1 && isset($fenlei_result1["code"]) && $fenlei_result1["code"] == 1) {
                $categories_data = null;
                if (isset($fenlei_result1["data"]) && is_array($fenlei_result1["data"])) {
                    $categories_data = $fenlei_result1["data"];
                } elseif (isset($fenlei_result1["data"]["data"]) && is_array($fenlei_result1["data"]["data"])) {
                    $categories_data = $fenlei_result1["data"]["data"];
                }

                if ($categories_data) {
                    $test_results[$api_name]['categories_found'] = count($categories_data);
                    $test_results[$api_name]['sample_categories'] = array_slice($categories_data, 0, 3);
                }
            }
        }

        exit(json_encode([
            'code' => 1,
            'msg' => '29系统分类API测试完成',
            'platform_info' => array(
                'name' => $a["name"],
                'pt' => $a["pt"],
                'url' => $a["url"]
            ),
            'test_results' => $test_results
        ]));
    } else {
        exit('{"code":-1,"msg":"无权限"}');
    }
    break;

    case 'startIntegrationSelected':
    if ($userrow['uid'] == 1) {
        $hid = trim($_POST['hid']);
        $localCategoryId = trim($_POST['localCategoryId']);
        $cids = json_decode($_POST['cids'], true);
        $markupMultiplier = floatval($_POST['markupMultiplier']);
        $multiplyByFive = intval($_POST['multiplyByFive']);
        $skipExisting = intval($_POST['skipExisting']);
        $createNewCategory = intval($_POST['createNewCategory']);
        $newCategoryName = trim($_POST['newCategoryName']);

        $sql = "SELECT * FROM qingka_wangke_huoyuan WHERE hid = ?";
        $a = $DB->prepare_getrow($sql, [$hid]);
        if (!$a) {
            jsonReturn(-1, "货源信息不存在");
        }

        if ($createNewCategory === 1) {
            if (empty($newCategoryName)) {
                jsonReturn(-1, "请输入新建分类的名称");
            }

            $sql = "INSERT INTO qingka_wangke_fenlei (sort, name, status, time) VALUES ('0', ?, '1', NOW())";
            $DB->prepare_query($sql, [$newCategoryName]);

            $sql = "SELECT * FROM qingka_wangke_fenlei WHERE name = ? ORDER BY id DESC LIMIT 1";
            $b = $DB->prepare_getrow($sql, [$newCategoryName]);

            if (!$b) {
                jsonReturn(-1, "新建分类失败");
            }
            $localCategoryId = $b['id'];
        } elseif (empty($localCategoryId)) {
            jsonReturn(-1, "请选择上架分类");
        }
        $data = array("uid" => $a["user"], "key" => $a["pass"]);
        $er_url = "{$a["url"]}/api.php?act=getclass";
        $result = get_url($er_url, $data);
        $result1 = json_decode($result, true);

        if (json_last_error() !== JSON_ERROR_NONE || !isset($result1["data"])) {
            jsonReturn(-1, "API 返回数据格式错误或缺失");
        }
        $categories = $result1["data"];
        $numItemsInserted = 0;
        $sql = "SELECT MAX(sort) AS max_sort FROM qingka_wangke_class WHERE fenlei = ?";
        $maxSortRow = $DB->prepare_getrow($sql, [$localCategoryId]);
        $maxSort = $maxSortRow ? $maxSortRow['max_sort'] : 0;
        foreach ($categories as $value) {
            if (!in_array($value['cid'], $cids)) {
                continue;
            }
            switch ($multiplyByFive) {
                case 2:
                    $price = $value['price'] * $markupMultiplier * 5;
                    break;
                case 1:
                    $price = $value['price'] * $markupMultiplier;
                    break;
                case 0:
                    $price = $value['price'] + $markupMultiplier;
                    break;
                default:
                    $price = $value['price'];
                    break;
            }
            if ($skipExisting) {
                $sql = "SELECT * FROM qingka_wangke_class WHERE docking = ? AND noun = ?";
                $existingProduct = $DB->prepare_getrow($sql, [$hid, $value['cid']]);
                if ($existingProduct) {
                    continue;
                }
            }
            $sort = $maxSort + 1;
            $sql = "INSERT INTO qingka_wangke_class (name, getnoun, noun, fenlei, queryplat, docking, price, sort, content, addtime, status)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), '1')";
            $DB->prepare_query($sql, [$value['name'], $value['cid'], $value['cid'], $localCategoryId, $hid, $hid, $price, $sort, $value['content']]);
            $maxSort++;
            $numItemsInserted++;
        }
        jsonReturn(1, "本次对接上架了{$numItemsInserted}个商品");
    } else {
        exit('{"code":-1,"msg":"无权限"}');
    }
    break;
case 'startintegration':
    if ($userrow['uid'] == 1) {
        $hid = trim(strip_tags(daddslashes($_POST['hid'])));
        $upstreamCategoryId = trim(strip_tags(daddslashes($_POST['upstreamCategoryId'])));
        $localCategoryId = trim(strip_tags(daddslashes($_POST['localCategoryId'])));
        $markupMultiplier = trim(strip_tags(daddslashes($_POST['markupMultiplier'])));
        $multiplyByFive = (int)daddslashes($_POST['multiplyByFive']);
        $skipExisting = (int)daddslashes($_POST['skipExisting']);
        $createNewCategory = intval($_POST['createNewCategory']);
        $newCategoryName = trim($_POST['newCategoryName']);

        $sql = "SELECT * FROM qingka_wangke_huoyuan WHERE hid = ?";
        $a = $DB->prepare_getrow($sql, [$hid]);
        if (!$a) {
            jsonReturn(-1, "货源信息不存在");
        }

        if ($createNewCategory === 1) {
            if (empty($newCategoryName)) {
                jsonReturn(-1, "请输入新建分类的名称");
            }
            $sql = "INSERT INTO qingka_wangke_fenlei (sort, name, status, time) VALUES ('0', ?, '1', NOW())";
            $DB->prepare_query($sql, [$newCategoryName]);
            $sql = "SELECT * FROM qingka_wangke_fenlei WHERE name = ? ORDER BY id DESC LIMIT 1";
            $b = $DB->prepare_getrow($sql, [$newCategoryName]);
            if (!$b) {
                jsonReturn(-1, "新建分类失败");
            }
            $localCategoryId = $b['id'];
        } elseif (empty($localCategoryId)) {
            jsonReturn(-1, "请选择上架分类");
        }

        $data = array("uid" => $a["user"], "key" => $a["pass"]);
        $er_url = "{$a["url"]}/api.php?act=getclass";
        $result = get_url($er_url, $data);
        $result1 = json_decode($result, true);
        if (json_last_error() !== JSON_ERROR_NONE || !isset($result1["data"])) {
            jsonReturn(-1, "API 返回数据格式错误或缺失");
        }
        $categories = $result1["data"];
        $numItemsInserted = 0;
        $maxSortRow = $DB->get_row("SELECT MAX(sort) AS max_sort FROM qingka_wangke_class WHERE fenlei='{$localCategoryId}'");
        $maxSort = $maxSortRow ? $maxSortRow['max_sort'] : 0;
        foreach ($categories as $value) {
            if ($upstreamCategoryId !== 'all' && $value['fenlei'] != $upstreamCategoryId) {
                continue;
            }
            switch ($multiplyByFive) {
                case 2:
                    $price = $value['price'] * $markupMultiplier * 5;
                    break;
                case 1:
                    $price = $value['price'] * $markupMultiplier;
                    break;
                case 0:
                    $price = $value['price'] + $markupMultiplier;
                    break;
                default:
                    $price = $value['price'];
                    break;
            }
            if ($skipExisting) {
                $existingProduct = $DB->get_row("SELECT * FROM qingka_wangke_class WHERE docking='{$hid}' AND noun='{$value['cid']}'");
                if ($existingProduct) {
                    continue;
                }
            }
            $sort = $maxSort + 1;
            $DB->query("INSERT INTO qingka_wangke_class (name, getnoun, noun, fenlei, queryplat, docking, price, sort, content, addtime, status)
                        VALUES ('{$value['name']}', '{$value['cid']}', '{$value['cid']}', '{$localCategoryId}', '$hid', '$hid', '{$price}', '{$sort}', '{$value['content']}', NOW(), '1')");
            $maxSort++;
            $numItemsInserted++;
        }
        jsonReturn(1, "本次对接上架了{$numItemsInserted}个商品");
    } else {
        exit('{"code":-1,"msg":"无权限"}');
    }
    break;

 case 'deleteDuplicates':
        if ($userrow['uid'] == 1) {
            $scope = $_POST['scope'];
            $scopeId = $_POST['scopeId'];
            $strategy = $_POST['strategy'];
            
            $where = '';
            $params = [];
            
            if ($scope == 'category') {
                $where = "AND t1.fenlei = ?";
                $params = [$scopeId];
            } elseif ($scope == 'docking') {
                $where = "AND t1.docking = ?";
                $params = [$scopeId];
            }
            
            if ($strategy == 'delall') {
                // 删除分类内所有商品
                $sql = "DELETE t1 FROM qingka_wangke_class t1 
                        WHERE 1=1 $where";
                $result = $DB->prepare_query($sql, $params);
            } else {
                // 原有的删除重复逻辑
                $order = ($strategy == 'keep_larger') ? 't1.cid < t2.cid' : 't1.cid > t2.cid';
                $sql = "DELETE t1 FROM qingka_wangke_class t1
                        JOIN qingka_wangke_class t2
                        ON t1.noun = t2.noun AND t1.docking = t2.docking AND t1.fenlei = t2.fenlei $where
                        WHERE $order";
                $result = $DB->prepare_query($sql, $params);
            }
            
            if ($result) {
                echo json_encode(['code' => 1, 'msg' => '操作成功']);
            } else {
                echo json_encode(['code' => 0, 'msg' => '操作失败']);
            }
        } else {
            exit('{"code":-1,"msg":"无权限"}');
        }
        break;
    case 'updateprice':
        if ($userrow['uid'] == 1) {
            $hid = $_POST['hid'];
            $upstreamCategoryId = $_POST['upstreamCategoryId'];
            $priceRatio = $_POST['priceRatio'];
            $multiplyByFive = $_POST['multiplyByFive'];

            $a = $DB->prepare_getrow("SELECT * FROM qingka_wangke_huoyuan WHERE hid=?", [$hid]);

            if (!$a) {
                jsonReturn(-1, "货源信息不存在");
            }

            $data = ["uid" => $a["user"], "key" => $a["pass"]];
            $er_url = "{$a["url"]}/api.php?act=getclass";

            $result = get_url($er_url, $data);
            $result1 = json_decode($result, true);

            if (json_last_error() !== JSON_ERROR_NONE || !isset($result1["data"])) {
                jsonReturn(-1, "API 返回数据格式错误或缺失");
            }

            $categories = $result1["data"];
            $numItemsUpdated = 0;

            if ($multiplyByFive == '5') {
                $sql = "UPDATE qingka_wangke_class SET status=0 WHERE docking=?";
                $DB->prepare_query($sql, [$hid]);
            }

            foreach ($categories as $value) {
                if ($upstreamCategoryId && $value['fenlei'] != $upstreamCategoryId) {
                    continue;
                }

                switch ($multiplyByFive) {
                    case '2':
                        $price = $value['price'] * $priceRatio * 5;
                        break;
                    case '1':
                        $price = $value['price'] * $priceRatio;
                        break;
                    case '0':
                        $price = $value['price'] + $priceRatio;
                        break;
                    default:
                        $price = $value['price'];
                        break;
                }

                $existingProduct = $DB->prepare_getrow("SELECT * FROM qingka_wangke_class WHERE docking=? AND noun=?", [$hid, $value['cid']]);

                if ($existingProduct) {
                    $updateFields = [];
                    if ($multiplyByFive != '3' && $multiplyByFive != '4' && $multiplyByFive != '5') {
                        $updateFields['price'] = $price;
                    }
                    if ($multiplyByFive == '3' || $multiplyByFive == '4') {
                        $updateFields['content'] = $value['content'];
                    }
                    if ($multiplyByFive == '4') {
                        $updateFields['name'] = $value['name'];
                    }
                    if ($multiplyByFive == '5') {
                        $updateFields['status'] = 1;
                    }

                    $updateParams = [];
                    $updateQuery = "UPDATE qingka_wangke_class SET ";
                    $updates = [];

                    foreach ($updateFields as $key => $val) {
                        $updates[] = "$key=?";
                        $updateParams[] = $val;
                    }

                    $updateQuery .= implode(", ", $updates);
                    $updateQuery .= " WHERE docking=? AND noun=?";
                    $updateParams[] = $hid;
                    $updateParams[] = $value['cid'];

                    $DB->prepare_query($updateQuery, $updateParams);
                    $numItemsUpdated++;
                }
            }
            jsonReturn(1, "本次更新了{$numItemsUpdated}个商品的价格或内容");
        }
        break;
    //密价模块
    case 'mijialist':
    $page = trim(strip_tags(daddslashes($_POST['page'])));
    $uid = trim(strip_tags(daddslashes($_POST['uid'])));
    $cid = trim(strip_tags(daddslashes($_POST['cid'])));
    $pagesize = 5000;
    $pageu = ($page - 1) * $pagesize; // 当前界面		
    if ($userrow['uid'] != '1') {
        jsonReturn(-1, "滚");
    }

    $sql = "";
    if ($uid != '') {
        $sql .= "where uid='$uid'";
    }
    if ($cid != '' && $sql == '') {
        $sql .= "where cid='$cid'";
    } elseif ($cid != '') {
        $sql .= " and cid='$cid'";
    }

    $a = $DB->query("select * from qingka_wangke_mijia {$sql} limit $pageu, $pagesize");
    $count1 = $DB->count("select count(*) from qingka_wangke_mijia {$sql}");
    while ($row = $DB->fetch($a)) {
        $r = $DB->get_row("select * from qingka_wangke_class where cid='{$row['cid']}'");
        $row['name'] = $r['name'];
        $data[] = $row;
    }
    $last_page = ceil($count1 / $pagesize); // 取最大页数
    $data = array('code' => 1, 'data' => $data, "current_page" => (int)$page, "last_page" => $last_page, "uid" => $userrow['uid']);
    exit(json_encode($data));
break;


case 'mijia':
    $data = $_POST['data'];
    $active = trim($_POST['active']);
    $uid = trim($data['uid']);
    $mid = trim($data['mid']);
    $mode = trim($data['mode']);
    $cid = $data['cid'];
    $price = trim($data['price']);
    $realmode = $mode;
    $realprice = $price;

    if ($userrow['uid'] != '1') {
        jsonReturn(-1, "不知道你在干什么");
    }
    if ($active == '1') {
        $type = trim($data['type']);

        if ($type == 1) {
            foreach ($cid as $row) {
                $sql = "SELECT * FROM qingka_wangke_class WHERE cid = ?";
                $origin_row = $DB->prepare_getrow($sql, [$row]);
                $originprice = $origin_row['price'];
                if ($realmode == 3) {
                    $price = $originprice * $realprice;
                    $mode = 2;
                }
                $sql = "DELETE FROM qingka_wangke_mijia WHERE uid = ? AND cid = ?";
                $DB->prepare_query($sql, [$uid, $row]);
                $sql = "INSERT INTO qingka_wangke_mijia (uid, cid, mode, price, addtime) VALUES (?, ?, ?, ?, ?)";
                $DB->prepare_query($sql, [$uid, $row, $mode, $price, $date]);
            }
            jsonReturn(1, "添加成功");
        } elseif ($type == 3) {
            $fid = $cid;
            foreach ($fid as $row) {
                $sql = "SELECT * FROM qingka_wangke_class WHERE fenlei = ?";
                $fenlei_class = $DB->prepare_query($sql, [$row]);
                while ($row = $DB->fetch($fenlei_class)) {
                    $originprice = $row['price'];
                    if ($realmode == 3) {
                        $price = $originprice * $realprice;
                        $mode = 2;
                    }
                    $sql = "DELETE FROM qingka_wangke_mijia WHERE uid = ? AND cid = ?";
                    $DB->prepare_query($sql, [$uid, $row['cid']]);
                    $sql = "INSERT INTO qingka_wangke_mijia (uid, cid, mode, price, addtime) VALUES (?, ?, ?, ?, ?)";
                    $DB->prepare_query($sql, [$uid, $row['cid'], $mode, $price, $date]);
                }
            }
            jsonReturn(1, "添加成功");
        } else {
            $sql = "DELETE FROM qingka_wangke_mijia WHERE uid = ? AND cid = ?";
            $DB->prepare_query($sql, [$uid, $cid]);
            $sql = "INSERT INTO qingka_wangke_mijia (uid, cid, mode, price, addtime) VALUES (?, ?, ?, ?, ?)";
            $DB->prepare_query($sql, [$uid, $cid, $mode, $price, $date]);
            jsonReturn(1, "添加成功");
        }
    } elseif ($active == '2') {
        $sql = "UPDATE qingka_wangke_mijia SET `price` = ?, `mode` = ?, `uid` = ?, `cid` = ? WHERE mid = ?";
        $DB->prepare_query($sql, [$price, $mode, $uid, $cid, $mid]);
        jsonReturn(1, "修改成功");
    } else {
        jsonReturn(-1, "不知道你在干什么");
    }
    break;

        
	case 'mijia_del':
	    $mid=daddslashes($_POST['mid']);
        if($userrow['uid']!='1'){jsonReturn(-1,"滚");}
    	$DB->query("delete from qingka_wangke_mijia where mid='$mid' ");
    	jsonReturn(1,"删除成功");
	break;
	
	case 'mijiaxiugai':
    if ($userrow['uid'] == 1) {
        if (isset($_POST['updates']) && is_array($_POST['updates'])) {
            $updates = $_POST['updates'];
            foreach ($updates as $update) {
                $mid = intval($update['mid']);
                $newPrice = floatval($update['newPrice']);
                $DB->query("UPDATE qingka_wangke_mijia SET price = '$newPrice' WHERE mid = '$mid'");
            }
            exit('{"code":1,"msg":"操作成功"}');
        } else {
            exit('{"code":-1,"msg":"无效的参数"}');
        }
    } else {
        exit('{"code":-2,"msg":"无权限"}');
    }
    break;

case 'batchdelete':
    if ($userrow['uid'] == 1) {
        if (isset($_POST['mids']) && is_array($_POST['mids'])) {
            $mids = $_POST['mids'];
            foreach ($mids as $mid) {
                $mid = intval($mid);
                $DB->query("DELETE FROM qingka_wangke_mijia WHERE mid = '$mid'");
            }
            exit('{"code":1,"msg":"操作成功"}');
        } else {
            exit('{"code":-1,"msg":"无效的参数"}');
        }
    } else {
        exit('{"code":-2,"msg":"无权限"}');
    }
    break;
}
?>